<?xml version="1.0" encoding="utf-8"?>

<layout
    xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <variable name="vm" type="com.dfl.smartscene.ccs.viewmodel.SurpriseEggListVM"/>
    </data>
    <RelativeLayout
        xmlns:app = "http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        tools:context=".SurpriseEggListFragment"
        android:id="@+id/surprise_egg_list_main"
        android:layout_width="@dimen/x_px_1488"
        android:layout_height="@dimen/y_px_612">

        <com.iauto.uicontrol.ImageBase
            android:layout_width="@dimen/x_px_1488"
            android:layout_height="@dimen/x_px_88"
            app:imageString = "@drawable/com_titleline_1488"/>

        <com.dfl.dflcommonlibs.uimodeutil.UIImageView
            android:id="@+id/button_content_top_close"
            android:layout_width="@dimen/x_px_40"
            android:layout_height="@dimen/y_px_40"
            android:layout_marginLeft="@dimen/x_px_40"
            android:layout_marginTop="@dimen/y_px_22"
            android:src="@drawable/ic48_system_closebig_n"
            android:contentDescription="@string/string_close_contentDescription"/>

        <com.dfl.dflcommonlibs.uimodeutil.UIImageView
            android:id="@+id/button_content_top_back"
            android:layout_width="@dimen/x_px_40"
            android:layout_height="@dimen/y_px_40"
            android:layout_marginLeft="@dimen/x_px_136"
            android:layout_marginTop="@dimen/y_px_22"
            android:scaleType="fitXY"
            android:src="@drawable/icon_back"
            android:contentDescription="@string/string_back_contentDescription"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.iauto.uicontrol.TextBase
            android:layout_width="@dimen/x_px_532"
            android:layout_height="@dimen/x_px_54"
            android:layout_marginStart="@dimen/x_px_478"
            android:layout_marginTop="@dimen/y_px_17"
            android:textColor="@color/color_text_title"
            android:textSize="@dimen/font_size_36"
            android:gravity="center_vertical|center_horizontal"
            android:text="@string/string_scene_pattern_menu_surprise"/>
        <com.iauto.uicontrol.ButtonView
            android:id="@+id/button_surprise_egg_list_edit"
            android:layout_width="@dimen/x_px_138"
            android:layout_height="@dimen/y_px_72"
            android:layout_marginTop="@dimen/y_px_8"
            android:layout_marginStart="@dimen/x_px_1318"
            app:hasAlphaMask="true"
            android:gravity="center"
            android:textSize="@dimen/font_size_36"
            android:textColor="@color/color_text_button"
            app:textString="@string/string_surprise_egg_list_edit"
            android:enabled="@{vm.isEditButtonEnable.booleanValue()}"
            android:visibility="gone"
            android:contentDescription="@string/string_surprise_egg_list_edit"
            app:onCcClick="onCCMethod_SurpriseEggEditClick">
        </com.iauto.uicontrol.ButtonView>

        <com.iauto.uicontrol.ButtonView
            android:id="@+id/button_surprise_egg_list_edit_end"
            android:layout_width="@dimen/x_px_138"
            android:layout_height="@dimen/y_px_72"
            android:layout_marginTop="@dimen/y_px_8"
            android:layout_marginStart="@dimen/x_px_1318"
            app:hasAlphaMask="true"
            android:gravity="center"
            android:textSize="@dimen/font_size_36"
            android:textColor="@color/color_text_button"
            android:visibility="gone"
            app:textString="@string/string_surprise_egg_list_edit_end"
            android:contentDescription="@string/string_surprise_egg_list_edit_end"
            app:onCcClick="onCCMethod_SurpriseEggEditEndClick">
        </com.iauto.uicontrol.ButtonView>

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refresh_layout_surprise_egg_list"
            android:layout_width="@dimen/x_px_1400"
            android:layout_height="@dimen/y_px_524"
            android:layout_marginStart="@dimen/x_px_44"
            android:layout_marginTop="@dimen/y_px_88">

            <com.iauto.uicontrol.RecyclerListView
                android:id="@+id/recycler_list_view_surprise_egg_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:tag="surpriseEggList"
                android:visibility="@{vm.listViewVisibility.intValue()}"
                app:animInfo=""
                app:edgeWidth_bottom="@dimen/x_px_40"
                app:edge_position="bottom"
                app:listColumnCount="3"
                app:listLayoutDirection="VERTICAL"
                app:listTopIndex="0"
                app:restriction="0"
                app:scrollBarName="scrollbar_view_usbaudiofoderlistfragment_folderlist"/>
        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

        <com.iauto.uicontrol.ScrollBar
            android:layout_marginStart="@dimen/x_px_1478"
            android:layout_marginTop="@dimen/y_px_104"
            android:layout_width="@dimen/x_px_10"
            android:layout_height="@dimen/y_px_492"
            android:id="@+id/scrollbar_view_surprise_egg_list"
            app:scrollThumbMinLength="158"
            app:scrollBarAutoHide="true"
            app:scrollBarAutoHideTime="4000"
            android:visibility="@{vm.listViewVisibility.intValue()}"
            >
            <com.iauto.uicontrol.ImageBase
                android:layout_marginStart="@dimen/x_px_2"
                android:layout_marginTop="0px"
                android:layout_width="@dimen/x_px_6"
                android:layout_height="@dimen/y_px_158"
                android:tag="scroll_thumb"
                app:imageString="com_scrollbar"
                >
            </com.iauto.uicontrol.ImageBase>

        </com.iauto.uicontrol.ScrollBar>

        <RelativeLayout
            android:id="@+id/surprise_egg_list_edit"
            android:layout_marginTop="@dimen/y_px_516"
            android:layout_marginStart="@dimen/x_px_354"
            android:layout_width="@dimen/x_px_780"
            android:layout_height="@dimen/y_px_96"
            android:clickable="true"
            android:visibility="@{vm.ListEditVisibility.intValue()}"
            >
            <com.iauto.uicontrol.ImageBase
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:imageString="@drawable/com_popbg">
            </com.iauto.uicontrol.ImageBase>
            <com.iauto.uicontrol.ButtonView
                android:id="@+id/button_surprise_egg_list_edit_check_all"
                android:layout_marginStart="@dimen/x_px_42"
                android:layout_marginTop="@dimen/y_px_18"
                android:layout_width="@dimen/x_px_60"
                android:layout_height="@dimen/y_px_60"
                app:hasAlphaMask="true"
                android:gravity="center"
                android:contentDescription="@string/string_surprise_egg_list_edit_all"
                app:ccSelected="@{vm.IsDelListItemAllSel.booleanValue()}"
                app:imageString="@drawable/com_multiplechoice_n">
            </com.iauto.uicontrol.ButtonView>

            <com.iauto.uicontrol.TextBase
                android:layout_marginStart="@dimen/x_px_116"
                android:layout_marginTop="@dimen/y_px_27"
                android:layout_width="@dimen/x_px_300"
                android:layout_height="@dimen/y_px_42"
                app:textString="@string/string_surprise_egg_list_edit_all"
                android:gravity="start|center_vertical"
                android:textColor="@color/color_text_list_del_text"
                android:textSize="@dimen/font_size_28">
            </com.iauto.uicontrol.TextBase>

            <com.iauto.uicontrol.ButtonView
                android:id="@+id/button_surprise_egg_list_edit_del"
                android:layout_width="@dimen/x_px_72"
                android:layout_height="@dimen/y_px_72"
                android:layout_marginTop="@dimen/y_px_12"
                android:layout_marginStart="@dimen/x_px_664"
                app:hasAlphaMask="true"
                android:gravity="center"
                android:textSize="@dimen/font_size_32"
                android:textColor="@color/color_text_list_del_button"
                android:enabled="@{vm.IsDelListItemSel.booleanValue()}"
                android:contentDescription="@string/string_surprise_egg_list_edit_del"
                app:textString="@string/string_surprise_egg_list_edit_del">
            </com.iauto.uicontrol.ButtonView>

        </RelativeLayout>

        <com.iauto.uicontrol.ImageBase
            android:layout_marginStart="@dimen/x_px_544"
            android:layout_marginTop="@dimen/y_px_160"
            android:layout_width="@dimen/x_px_400"
            android:layout_height="@dimen/y_px_200"
            android:visibility="@{vm.noFilesVisibility.intValue()}"
            app:imageString="@drawable/bg_com_emptystatus"/>

        <com.iauto.uicontrol.TextBase
            android:layout_marginStart="@dimen/x_px_284"
            android:layout_marginTop="@dimen/y_px_368"
            android:layout_width="@dimen/x_px_920"
            android:layout_height="@dimen/y_px_86"
            android:textColor="@color/color_text_explain"
            android:textSize="@dimen/font_size_28"
            android:gravity="center"
            android:visibility="@{vm.noFilesVisibility.intValue()}"
            android:text="@string/string_surprise_egg_list_no_files_message"/>

        <com.iauto.uicontrol.ImageBase
            android:id="@+id/image_view_network_error"
            android:layout_marginStart="@dimen/x_px_544"
            android:layout_marginTop="@dimen/y_px_142"
            android:layout_width="@dimen/x_px_384"
            android:layout_height="@dimen/y_px_192"
            android:visibility="@{vm.netDisconnectVisibility.intValue()}"
            app:imageString="@drawable/bg_com_networkexception"/>

        <com.iauto.uicontrol.TextBase
            android:id="@+id/text_view_network_error"
            android:layout_marginTop="@dimen/y_px_8"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_text_explain"
            android:layout_below="@id/image_view_network_error"
            android:textSize="@dimen/font_size_28"
            android:layout_centerHorizontal="true"
            android:visibility="@{vm.netDisconnectVisibility.intValue()}"
            android:text="@string/string_surprise_egg_list_net_disconnect_message"/>

        <com.iauto.uicontrol.ButtonView
        android:id="@+id/text_view_network_retry"
            android:layout_width="@dimen/x_px_184"
            android:layout_height="@dimen/y_px_54"
            android:background="@drawable/bg_button_positive_white"
            android:text="重试"
            android:layout_below="@id/text_view_network_error"
            android:textColor="@color/white"
            android:gravity="center"
            app:onCcClick="onCCMethod_SurpriseEggRetryClick"
            android:visibility="@{vm.netDisconnectVisibility.intValue()}"
            android:textSize="@dimen/font_size_26"
            android:layout_marginTop="@dimen/y_px_51"
            android:layout_centerHorizontal="true"/>

        <include
            android:id="@+id/layout_page_loading"
            layout="@layout/layout_page_loading"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true" />
    </RelativeLayout>



</layout>

