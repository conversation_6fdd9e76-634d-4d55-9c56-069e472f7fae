package com.dfl.smartscene.ccs.model;

import androidx.annotation.Nullable;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dfl.dflcommonlibs.log.LogUtil;
import com.dfl.dflcommonlibs.rxbus.RxBus;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.base.BaseEmitter;
import com.dfl.smartscene.ccs.bean.CarConfig;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SceneCategory;
import com.dfl.smartscene.ccs.busevent.CollectEvent;
import com.dfl.smartscene.ccs.constant.ConstantModelValue;
import com.dfl.smartscene.ccs.constant.ConstantValue;
import com.dfl.smartscene.ccs.factory.SpecialBeanFactory;
import com.dfl.smartscene.ccs.http.HttpResult;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.model.manager.CarConfigManager;
import com.dfl.smartscene.ccs.model.manager.CustomApiManager;
import com.dfl.smartscene.ccs.model.manager.NetworkManager;
import com.dfl.smartscene.ccs.model.manager.SceneConditionManager;
import com.dfl.smartscene.ccs.model.manager.VideoManager;
import com.dfl.smartscene.ccs.util.CarConfigUtil;
import com.dfl.smartscene.ccs.util.ListUtil;
import com.dfl.smartscene.ccs.util.ToastManager;
import com.dfl.smartscene.ccs.wrapper.HttpWrapper;
import com.iauto.uibase.utils.MLog;
import com.tencent.mmkv.MMKV;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.BiFunction;
import io.reactivex.functions.Consumer;
import io.reactivex.functions.Function;
import io.reactivex.schedulers.Schedulers;

/**
 * <AUTHOR> Zezheng
 * @date ：2022/7/13 9:23
 * @description ：管理场景数据的初始化，用户场景数据的初始化
 */
public class SceneDataModel {

    private static final String TAG = "SceneDataModel";
    private static volatile SceneDataModel sInstance;
    private MMKV mMMKV = MMKV.defaultMMKV();
    private SceneSyncHelper mSceneSyncHelper = new SceneSyncHelper();

    private List<SceneCategory> userScenes;
    private List<SceneCategory> squareScenes;

    private List<SceneDataModifyListener> mSceneDataModifyListeners = new ArrayList<>();
    /**保存模式广场数据的key*/
    public static final String KEY_MMKV_SQUARE_SCENE = "KEY_MMKV_SQUARE_SCENE";
    public static final String KEY_MMKV_USER_SCENE = "KEY_MMKV_USER_SCENE";
    /**保存车辆动作配置数据的key*/
    public static final String KEY_MMKV_CAR_CONFIG = "KEY_MMKV_CAR_CONFIG";
    /**
     * 基于Map保存数据内容和时间的相应key值，组装为Map之后再做保存；读取则反之
     */
    public static final String DATA_KEY = "data";
    public static final String UPDATE_TIME_KEY = "update_time";
    /**30分钟内不重复请求数据*/
    public static final long UPDATE_INTERVAL = ConstantValue.REQUEST_DATA_TIMER;

    private final BaseEmitter<SceneBean> mExecuteSceneBeanEmitter = new BaseEmitter<>();
    private final BaseEmitter<List<SceneCategory>> mLocalUserScenesEmitter = new BaseEmitter<>();

    private SceneDataModel() {
    }

    public static SceneDataModel getInstance() {
        if (null == sInstance) {
            synchronized (SceneDataModel.class) {
                if (null == sInstance) {
                    sInstance = new SceneDataModel();
                }
            }
        }
        return sInstance;
    }



    /**
     * 获取用户收藏的场景列表
     * 如果内存里有数据，说明开机请求过了，直接返回内存数据
     * 如果内存里没数据，说明是开机第一次请求，
     * 1.如果helper类中有之前未和云端同步成功的数据，走云端同步接口
     * 2.如果helper类中没有之前未和云端同步成功的数据，走获取场景接口
     * 如果请求成功，使用新数据
     * 如果请求失败，使用本地数据
     *
     * @return
     */
    public Observable<List<SceneCategory>> requestUserSceneList(boolean refresh) {
        LogUtil.d(TAG, "requestUserSceneList");
        if (!refresh && userScenes != null && CarConfigManager.getInstance().getCarConfigDataStatus() != CarConfigManager.DATA_STATUS_NULL) {
            return Observable.just(userScenes);
        }
        Observable<List<SceneCategory>> request;
        if (mSceneSyncHelper.hasUnUpdatedScene() && mSceneSyncHelper.getDelBean().size() > 0) {//需要有待上传的场景
            MLog.d(TAG, "requestUserSceneList sync");
            request = HttpWrapper.getInstance().uploadLocalScenes(getLocalUserScene(), mSceneSyncHelper.getAddBeans(), mSceneSyncHelper.getDelBean())
                    .doOnNext(new Consumer<List<SceneCategory>>() {
                        @Override
                        public void accept(List<SceneCategory> sceneCategories) throws Exception {
                            MLog.d(TAG, "requestUserSceneList sync callback");
                            mSceneSyncHelper.onAllUpdateSuccess();
                        }
                    });
        } else {
            MLog.d(TAG, "requestUserSceneList cloud");
            if(refresh){
                mMMKV.remove(KEY_MMKV_USER_SCENE);
            }
            request = HttpWrapper.getInstance().requestUserSceneList();
        }
        request = request.onErrorReturn(new Function<Throwable, List<SceneCategory>>() {
            @Override
            public List<SceneCategory> apply(Throwable throwable) throws Exception {
                LogUtil.d(TAG, "uploadLocalScenes onErrorReturn 1 :" + throwable.getMessage());
                userScenes = getLocalUserScene();
                return userScenes;
            }
        });
        return Observable.zip(request, CarConfigManager.getInstance().initCarConfig(), new BiFunction<List<SceneCategory>, CarConfig, List<SceneCategory>>() {
            @Override
            public List<SceneCategory> apply(List<SceneCategory> sceneCategories, CarConfig carConfig) throws Exception {
                MLog.d(TAG, "requestUserSceneList zip , carconfig");
                return sceneCategories;
            }
        }).doOnNext(new Consumer<List<SceneCategory>>() {
            @Override
            public void accept(List<SceneCategory> sceneCategories) throws Exception {
                LogUtil logUtil = new LogUtil();
                logUtil.printJsonLogger(TAG, "requestUserSceneList return , data");

                if (sceneCategories != null) {
                    userScenes = sceneCategories;
                    saveLocalUserScene(sceneCategories);
                    for (SceneCategory sceneCategory : sceneCategories) {
                        for (SceneBean sceneBean : sceneCategory.getSceneBeanList()) {
                            LogUtil.d(TAG, "apply scene name :"+sceneBean.getSceneName()+" time = "+sceneBean.getTimestamp());
                            if (sceneBean.isAutoEnable()) {
                                SceneConditionManager.getInstance().registerSceneNormalCondition(sceneBean);
                            }
                            if (CustomApiManager.getInstance().getIUploadHotwords() != null) {
                                SceneConditionManager.getInstance().registerVrCondition(sceneBean);
                            }
                        }
                    }
                }
                RxBus.getDefault().post(new CollectEvent(ConstantModelValue.CATEGORY_ID_COLLECT, null, false));

            }
        });
    }

    /**
     * 获取场景列表中的场景数量
     * @param list
     * @return
     */
    private int getCategoriesSceneSize(List<SceneCategory> list){
        int size = 0;
        if(list != null){
            for(SceneCategory sceneCategory :list){
                if(sceneCategory.getSceneBeanList() != null){
                    size = size +sceneCategory.getSceneBeanList().size();
                }
            }
        }
        return size;
    }

    /**
     * 对用户某一分类下的场景向云端同步换序信息
     *
     * @param categoryId
     * @param sceneBeans
     */
    public void sortUserScene(String categoryId, List<SceneBean> sceneBeans) {
        LogUtil.d(TAG, "sort user scene");
        SceneCategory userCategory = findUserCategoryById(categoryId);
        if (userCategory == null) {
            return;
        }
        userCategory.setSceneBeanList(sceneBeans);
        int index = -1;
        for (SceneCategory category : userScenes) {
            if (category.getCategoryId().equals(categoryId)) {
                index = userScenes.indexOf(category);
                break;
            }
        }
        //替换原来内存的数据
        userScenes.set(index, userCategory);
        saveLocalUserScene(userScenes);
        mSceneSyncHelper.onSortScene();
        HttpWrapper.getInstance().syncSceneSort(userCategory).subscribe(new Observer<HttpResult>() {
            @Override
            public void onSubscribe(Disposable d) {
            }

            @Override
            public void onNext(HttpResult httpResult) {
                mSceneSyncHelper.onSortSceneSuccess();
            }

            @Override
            public void onError(Throwable e) {
            }

            @Override
            public void onComplete() {
            }
        });
//        RxBus.getDefault().post(new CollectEvent(CollectEvent.COLLECT_EVENT_TYPE_ALL, categoryId));

    }

    /**
     * 添加用户收藏场景
     *
     * @param sceneBean
     */
    public synchronized void addUserScene(String categoryId, SceneBean sceneBean) {
        if(sceneBean == null){
            return;
        }
        sceneBean.setTimestamp(System.currentTimeMillis());
        if (sceneBean.isAutoEnable()) {
            SceneConditionManager.getInstance().registerSceneNormalCondition(sceneBean);
        }
        SceneConditionManager.getInstance().registerVrCondition(sceneBean);
        addLocalUserScene(-1,categoryId, sceneBean);
        addCloudUserScene(categoryId, sceneBean);
        notifySceneAdd(sceneBean);
        if(userScenes != null){
            mLocalUserScenesEmitter.onNext(userScenes);
        }
    }


    /**
     * 把新增的场景保存到本地
     *
     * @param categoryId
     * @param sceneBean
     */
    public void addLocalUserScene(int sortIndex,String categoryId, SceneBean sceneBean) {
        if(userScenes == null){
            return;
        }
        if (!categoryId.equals(ConstantModelValue.CATEGORY_ID_CUSTOM) && CarConfigUtil.isCCS()) {
            categoryId = ConstantModelValue.CATEGORY_ID_COLLECT;
        }
        //添加
        boolean flag = false;
        SceneCategory categoryTemp = null;
        for (SceneCategory sceneCategory : userScenes) {
            if(sceneCategory.getCategoryId().equals(categoryId)){
                flag = true;
                categoryTemp = sceneCategory;
            }
            if(!flag){
                continue;
            }
        }
        //需要确保新添加的场景sort值最小
        if(categoryTemp != null){
            int minSort = 0;
            int maxSort = 0;

            for(SceneBean bean : categoryTemp.getSceneBeanList()){
                LogUtil.d(TAG, "add local user scene sort " + bean.getSort());
                minSort = (Math.min(bean.getSort(), minSort));
                maxSort = Math.max(bean.getSort(), maxSort);
            }
            if (sortIndex == -1) {
                sceneBean.setSort(maxSort + 1);
                LogUtil.d(TAG, "addLocalUserScene: "+ " id "+ sceneBean.getSceneId() +sceneBean.getSceneName()+" ,, "+ sceneBean.getSort());
                //数据插入到最后一位
                categoryTemp.addScene(categoryTemp.getSceneBeanList().size(), sceneBean);
                LogUtil.d(TAG, "add local user scene maxSort " + maxSort + ",minSort " + minSort);
            }else {
                LogUtil.d(TAG, "add local user scene editPos sortIndex " + sortIndex);
                if(sortIndex >= categoryTemp.getSceneBeanList().size()){
                    sortIndex = categoryTemp.getSceneBeanList().size();
                }
                categoryTemp.addScene(sortIndex,sceneBean);
            }
            LogUtil.d(TAG, "add local user scene");
            //向后台同步排序数据
//            sortUserScene(categoryTemp.getCategoryId(),categoryTemp.getSceneBeanList());
            //添加数据到本地
//            SceneCategory userCategory = findUserCategoryById(categoryTemp.getCategoryId());
//            if (userCategory != null) {
//                userCategory.setSceneBeanList(categoryTemp.getSceneBeanList());
//                int index = -1;
//                for (SceneCategory category : userScenes) {
//                    if (category.getCategoryId().equals(categoryTemp.getCategoryId())) {
//                        index = userScenes.indexOf(category);
//                        break;
//                    }
//                }
//                //替换原来内存的数据
//                if(index != -1){
//                    userScenes.set(index, userCategory);
//                }
//            }
        }


        saveLocalUserScene(userScenes);
    }

    /**
     * 把新增的场景保存到云端
     *
     * @param categoryId
     * @param sceneBean
     */
    public void addCloudUserScene(String categoryId, SceneBean sceneBean) {
        mSceneSyncHelper.onAddScene(sceneBean);
        Observable<HttpResult> httpResultObservable = HttpWrapper.getInstance().addUserScene(categoryId, sceneBean);
        httpResultObservable.subscribe(new Observer<HttpResult>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(HttpResult httpResult) {
                if (httpResult != null) {
                    if ("1".equals(httpResult.getResult())) {
                        mSceneSyncHelper.onAddSuccess(sceneBean);
                        if (httpResult.getRows() != null) {
                            List<SceneCategory> returnCategories = JSON.parseObject(JSON.toJSONString(httpResult.getRows()), new TypeReference<List<SceneCategory>>() {
                            });
                            //中间取值遍历，提供运算性能
                            for(int i = 0 ; i < userScenes.size() ; i ++){
                                SceneCategory userCategory = userScenes.get(i);
                                SceneCategory returnCategory = null;
                                int j = i;
                                do{
                                    if(returnCategories.get(j).getCategoryId().equals(userCategory.getCategoryId())){
                                        returnCategory = returnCategories.get(j);
                                        break;
                                    }
                                    j += 1;
                                    if(j == returnCategories.size()){
                                        j = 0;
                                    }
                                }while ( i != j);
                                if(returnCategory !=null){
                                    for(int i1 = 0 ; i1 < userCategory.getSceneBeanList().size() ; i1 ++){
                                        SceneBean userScene = userCategory.getSceneBeanList().get(i1);
                                        SceneBean returnScene = null;
                                        int j1 = i1;
                                        do{
                                            if(returnCategory.getSceneBeanList().get(j1).getSceneId().equals(userScene.getSceneId())){
                                                returnScene = returnCategory.getSceneBeanList().get(j1);
                                                break;
                                            }
                                            j1 += 1;
                                            if(j1 == returnCategory.getSceneBeanList().size()){
                                                j1 = 0;
                                            }
                                        }while ( i1 != j1);
                                        if(returnScene != null){
                                            userScene.setSort(returnScene.getSort());
                                        }

                                    }
                                }
                            }
                        }
                        MLog.d(TAG, "addUserScene is success, scene category id = "+sceneBean.getSceneCategoryId());
                    } else {
                        MLog.e(TAG, "addUserScene is fail.");
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                MLog.e(TAG, "addUserScene onError, msg = " + e.toString());
            }

            @Override
            public void onComplete() {

            }
        });

    }

    /**
     * 删除用户场景（单条操作，以及多条操作）
     * 注意事项：多条操作时，仅告知执行的第一条id的结果即可
     * @param categoryId
     * @param sceneBeans
     */
    public synchronized void deleteUserScenes(String categoryId, List<SceneBean> sceneBeans) {
        if(sceneBeans == null){
            return;
        }
        if(NetworkManager.getInstance().hasNetwork()){
            deleteLocalUserScene(categoryId,sceneBeans);
            deleteCloudUserScene(categoryId,sceneBeans);
            for (int i = 0; i < sceneBeans.size(); i++) {
                RxBus.getDefault().post(new CollectEvent(CollectEvent.COLLECT_EVENT_TYPE_SINGLE, sceneBeans.get(i).getSceneCategoryId(), sceneBeans.get(i).getSceneId(), false));
            }
        }else{
            ToastManager.showToast(ScenePatternApp.getInstance().getString(R.string.string_no_network_notice));
        }
    }

    public void deleteLocalUserScene(String categoryId, List<SceneBean> sceneBeans){
        for (SceneBean sceneBean : sceneBeans) {
            deleteLocalScene(categoryId, sceneBean.getSceneId());
            SceneConditionManager.getInstance().unregisterSceneCondition(sceneBean);
            SceneConditionManager.getInstance().unregisterVrCondition(sceneBean);
            notifySceneDelete(sceneBean);
        }

    }

    public void deleteCloudUserScene(String categoryId, List<SceneBean> sceneBeans){
        List<String> sceneIds = new ArrayList<>();
        for (SceneBean sceneBean : sceneBeans) {
            sceneIds.add(sceneBean.getSceneId());
            mSceneSyncHelper.onDeleteScene(sceneBean);
        }
        HttpWrapper.getInstance().deleteUserScene(categoryId, sceneIds).subscribe(new Observer<HttpResult>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(HttpResult httpResult) {
                if (httpResult != null) {
                    if ("1".equals(httpResult.getResult())) {
                        MLog.d(TAG, "deleteUserScene is success.");
                        for (SceneBean sceneBean : sceneBeans) {
                            mSceneSyncHelper.onDeleteSuccess(sceneBean);
                        }
                    } else {
                        MLog.e(TAG, "deleteUserScene is fail.");
                    }
                }
            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onComplete() {

            }
        });

    }

    /**
     * 删除单个用户场景
     *
     * @param categoryId
     * @param sceneId
     */
    public void deleteLocalScene(String categoryId, String sceneId) {
        LogUtil.d(TAG, "delete local scene , categoryId : "+categoryId+", sceneId : "+sceneId);
        if (!categoryId.equals(ConstantModelValue.CATEGORY_ID_CUSTOM) && CarConfigUtil.isCCS()) {
            categoryId = ConstantModelValue.CATEGORY_ID_COLLECT;
        }

        String mCid = categoryId;
        SceneBean flag = null;
        if (mCid == null || "".equals(mCid)) {
            for (SceneCategory category : userScenes) {
                if ((flag = category.removeScene(sceneId)) != null) {
                    mCid = category.getCategoryId();
                }
            }
        } else {
            if(null == userScenes){
                return;
            }
            SceneCategory sceneCategory = null;
            int index = -1;
            for (SceneCategory category : userScenes) {
                if (category.getCategoryId().equals(mCid)) {
                    sceneCategory = category;
                    index = userScenes.indexOf(category);
                    break;
                }
            }

            if (sceneCategory != null) {
                flag = sceneCategory.removeScene(sceneId);
                //替换原来内存的数据
                userScenes.set(index, sceneCategory);
            }
        }
        if (flag == null) {
            return;
        }

        saveLocalUserScene(userScenes);
        //RxBus.getDefault().post(new CollectEvent(mCid, sceneId, false));
    }


    /**
     * 替换用户收藏场景
     *
     * @param categoryId
     * @param sceneBean
     */
    public synchronized void replaceUserScene(int editPoi,String categoryId, SceneBean sceneBean) {
        if(sceneBean == null){
            return;
        }
        long time = System.currentTimeMillis();
        sceneBean.setTimestamp(time);
        LogUtil.d(TAG, "replace scene time = "+time+",scene name = "+sceneBean.getSceneName());
        int oldIndex = findIndexOfSceneCategoryById(sceneBean.getSceneId());
        SceneCategory sceneCategory = findCategotyById(categoryId, userScenes);
        SceneBean oldScene = sceneCategory.findScene(sceneBean.getSceneId());
        if(oldScene != null){
            deleteLocalUserScene(categoryId, Arrays.asList(oldScene));
        }

        if (sceneBean.isAutoEnable()) {
            SceneConditionManager.getInstance().registerSceneNormalCondition(sceneBean);
        }
        SceneConditionManager.getInstance().registerVrCondition(sceneBean);
        if(oldIndex != -1){
            addLocalUserScene(oldIndex,categoryId,sceneBean);
        }else {
            addLocalUserScene(sceneCategory.getSceneBeanList().size(),categoryId,sceneBean);
        }
        mSceneSyncHelper.onReplaceScene(sceneBean);
        Observable<HttpResult> httpResultObservable = HttpWrapper.getInstance().replaceUserScene(categoryId, sceneBean);
        httpResultObservable.subscribe(new Observer<HttpResult>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(HttpResult httpResult) {
                if (httpResult != null) {
                    if ("1".equals(httpResult.getResult())) {
                        mSceneSyncHelper.onReplaceSuccess(sceneBean);
                        MLog.d(TAG, "replaceUserScene is success.");
                    } else {
                        MLog.e(TAG, "replaceUserScene is fail.");
                    }
                }
            }

            @Override
            public void onError(Throwable e) {
                MLog.e(TAG, "replaceUserScene onError, msg = " + e.toString());
            }

            @Override
            public void onComplete() {
                requestCloudSceneList();
            }
        });
    }

    /**
     * 根据传入场景分类id和场景分类列表找到对应的场景分类
     *
     * @param id
     * @param categories
     * @return
     */
    public SceneCategory findCategotyById(String id, List<SceneCategory> categories) {
        if (categories == null) {
            return null;
        }
        for (SceneCategory sceneCategory : categories) {
            if (sceneCategory.getCategoryId().equals(id)) {
                return sceneCategory;
            }
        }
        return null;
    }

    public String getUserCategoryNameByCategoryId(String categoryId) {
        if (userScenes == null) {
            return null;
        }
        for (SceneCategory sceneCategory : userScenes) {
            if (categoryId.equals(sceneCategory.getCategoryId())) {
                return sceneCategory.getCategoryName();
            }
        }
        return null;
    }

    /**
     * 根据分类id查到用户分类
     *
     * @param id
     * @return
     */
    public SceneCategory findUserCategoryById(String id) {
        if(null == userScenes){
            return null;
        }
        SceneCategory sceneCategory;
        synchronized (userScenes){
            sceneCategory = findCategotyById(id, userScenes);
        }
        return sceneCategory;
    }

    /**
     * 根据分类id查到首页分类数据
     *
     * @param id
     * @return
     */
    public SceneCategory findLibraryCategoryById(String id) {
        return findCategotyById(id, squareScenes);
    }

    /**
     * 根据传入id查到对应的广场场景
     * 2023-2-18为了避免空指针的问题，将用户自定义场景也纳入查找范围
     *
     * @param id
     * @return
     */
    public SceneBean findLibrarySceneBeanById(String id) {
        if (squareScenes == null) {
            squareScenes = SceneDataModel.getInstance().getLocalSquareScene();
        }
        for (SceneCategory sceneCategory : squareScenes) {
            for (SceneBean sceneBean : sceneCategory.getSceneBeanList()) {
                if (id.equals(sceneBean.getSceneId())) {
                    return sceneBean;
                }
            }
        }
        return null;
    }

    /**
     * 根据传入id查到对应的广场场景
     * 2023-2-18为了避免空指针的问题，将用户自定义场景也纳入查找范围
     *
     * @param id
     * @return
     */
    public SceneBean findRelaxLibrarySceneBeanById(String id) {
        if (squareScenes == null) {
            squareScenes = SceneDataModel.getInstance().getLocalSquareScene();
        }
        if(ListUtil.isEmpty(squareScenes)){
            return null;
        }
        SceneCategory sceneCategory = squareScenes.get(0);
            for (SceneBean sceneBean : sceneCategory.getSceneBeanList()) {
                if (id.equals(sceneBean.getSceneId())) {
                    return sceneBean;
                }
            }
        return null;
    }


    /**
     * 根据传入id查到对应的广场场景
     *
     * @param id
     * @return
     */
    public SceneBean findUserSceneBeanById(String id) {
        if (userScenes == null) {
            return null;
        }
        for (SceneCategory sceneCategory : userScenes) {
            for (SceneBean sceneBean : sceneCategory.getSceneBeanList()) {
                if (id.equals(sceneBean.getSceneId())) {
                    return sceneBean;
                }
            }
        }
        return null;
    }


    /**
     * 根据id查询在所属分类的目录中的index序号
     * @param id
     * @return
     */
    public int findIndexOfSceneCategoryById(String id){
        if (userScenes == null) {
            return -1;
        }
        for (SceneCategory sceneCategory : userScenes) {
            for (int i = 0; i < sceneCategory.getSceneBeanList().size(); i++) {
                if(id.equals(sceneCategory.getSceneBeanList().get(i).getSceneId())){
                    return i;
                }
            }
        }
        return -1;
    }


    /**
     * 请求云端返回的场景列表
     *
     * @return
     */
    public Observable<List<SceneCategory>> requestCloudSceneList() {
        LogUtil.d(TAG, "requestCloudSceneList: ");
        Observable<List<SceneCategory>> request = HttpWrapper.getInstance().requestSquareSceneList().doOnNext(new Consumer<List<SceneCategory>>() {
            @Override
            public void accept(List<SceneCategory> sceneCategories) throws Exception {
                LogUtil logUtil = new LogUtil();
                logUtil.printJsonLogger(TAG, "requestCloudSceneList return , data = " + JSON.toJSONString(sceneCategories));

                squareScenes = sceneCategories;
//                if (CarConfigUtil.isCCU()) {
//                    if (userScenes != null && userScenes.size() == 1) {
//                        for (SceneCategory squareCategory : squareScenes) {
//                            userScenes.add(userScenes.size() - 1, SpecialBeanFactory.productEmptyCategory(squareCategory));
//                        }
//                    }
//                }
            }
        }).onErrorReturn(new Function<Throwable, List<SceneCategory>>() {
            @Override
            public List<SceneCategory> apply(Throwable throwable) throws Exception {
                LogUtil.d(TAG,"requestCloudSceneList onErrorReturn");
                return getLocalSquareScene();
            }
        });
        return Observable.zip(request, CarConfigManager.getInstance().initCarConfig(), new BiFunction<List<SceneCategory>, CarConfig, List<SceneCategory>>() {
            @Override
            public List<SceneCategory> apply(List<SceneCategory> sceneCategories, CarConfig carConfig) throws Exception {
                LogUtil.d(TAG,"requestCloudSceneList return success");
                VideoManager.getInstance().parseScene(sceneCategories);

//                SceneCategory cardCategory = findLibraryCategoryById(ConstantModelValue.SCENE_CATEGORY_ID_LIBRARY_CARD);
//                if(cardCategory != null){
//                    for (SceneBean sceneBean : cardCategory.getSceneBeanList()) {
//                        SceneConditionManager.getInstance().registerVrCondition(sceneBean);
//                    }
//                }
                //请求到数据后将发现页场景全部注册热词
                List<SceneCategory> cardCategoryList = SceneDataModel.getInstance().getSquareScenes();
                if(cardCategoryList != null){
                    for(SceneCategory sceneCategory :cardCategoryList){
                        if(sceneCategory != null){
                            for (SceneBean sceneBean : sceneCategory.getSceneBeanList()) {
                                SceneConditionManager.getInstance().registerVrCondition(sceneBean);
                            }
                        }
                    }
                }
                return sceneCategories;
            }
        });
    }

    /**
     * 给广场场景添加收藏标签
     *
     * @param squareScenes
     */
    public void addSquareSceneCollectState(List<SceneCategory> squareScenes) {
        for (SceneCategory squareCategory : squareScenes) {
            addSquareSceneCollectState(squareCategory);
        }
    }

    /**
     * 给首页的场景数据添加收藏信息
     *
     * @param squareCategory
     */
    public void addSquareSceneCollectState(SceneCategory squareCategory) {
        if (!CarConfigUtil.isCCS()) {
            addSquareSceneCollectState(squareCategory.getCategoryId(), squareCategory.getSceneBeanList());
        } else {
            addSquareSceneCollectState(ConstantModelValue.CATEGORY_ID_COLLECT, squareCategory.getSceneBeanList());
        }
    }

    /**
     * 给首页的场景数据添加收藏信息
     */
    public void addSquareSceneCollectState(String categoryId, List<SceneBean> sceneBeans) {
        if (!categoryId.equals(ConstantModelValue.CATEGORY_ID_CUSTOM) && CarConfigUtil.isCCS()) {
            categoryId = ConstantModelValue.CATEGORY_ID_COLLECT;
        }


        SceneCategory userCategory = findCategotyById(categoryId, userScenes);
        if (userCategory == null) {
            return;
        }
        for (SceneBean squareBean : sceneBeans) {
            squareBean.setCollectState(false);
            for (SceneBean userBean : userCategory.getSceneBeanList()) {
                if (squareBean.getSceneId().equals(userBean.getSceneId())) {
                    squareBean.setCollectState(true);
                    break;
                }
            }
        }
    }

    public List<SceneCategory> getUserScenes() {
        return userScenes;
    }

    public  List<SceneCategory> getSquareScenes(){
        return squareScenes;
    }

    public void registerDataListener(SceneDataModifyListener sceneDataModifyListener) {
        mSceneDataModifyListeners.add(sceneDataModifyListener);
    }

    public void unregisterDataListener(SceneDataModifyListener sceneDataModifyListener) {
        mSceneDataModifyListeners.remove(sceneDataModifyListener);
    }

    public void notifySceneAdd(SceneBean sceneBean) {
        for (SceneDataModifyListener sceneDataModifyListener : mSceneDataModifyListeners) {
            sceneDataModifyListener.onSceneAdd(sceneBean);
        }
    }

    public void notifySceneReplace(SceneBean newScene) {
        for (SceneDataModifyListener sceneDataModifyListener : mSceneDataModifyListeners) {
            sceneDataModifyListener.onSceneReplace(newScene);
        }

    }

    public void notifySceneDelete(SceneBean sceneBean) {
        for (SceneDataModifyListener sceneDataModifyListener : mSceneDataModifyListeners) {
            sceneDataModifyListener.onSceneDelete(sceneBean);
        }

    }

    /**
     * 保存用户场景至本地
     *
     * @param sceneCategories
     */
    public void saveLocalUserScene(List<SceneCategory> sceneCategories) {
        mMMKV.encode(KEY_MMKV_USER_SCENE, JSON.toJSONString(sceneCategories));
    }

    /**
     * 获取本地保存的用户场景，若无，则根据广场场景创建分类
     *
     * @return
     */
    public List<SceneCategory> getLocalUserScene() {
        if(!mMMKV.containsKey(KEY_MMKV_USER_SCENE)){
            return null;
        }
        List<SceneCategory> result = JSON.parseObject(mMMKV.decodeString(KEY_MMKV_USER_SCENE), new TypeReference<List<SceneCategory>>() {
        });
        if (result == null) {
            result = new ArrayList<>();
            if (CarConfigUtil.isCCU() || CarConfigUtil.isPhone()) {
                if (squareScenes != null) {
                    for (SceneCategory squareCategory : squareScenes) {
                        result.add(SpecialBeanFactory.productEmptyCategory(squareCategory));
                    }
                }
            } else {
                result.add(SpecialBeanFactory.productCollectCategory());

            }
            result.add(SpecialBeanFactory.productCustomCategory());
        }
        return result;
    }

    public @Nullable
    List<SceneCategory> getStorageLocalSquareScene(){
        if(mMMKV.containsKey(SceneDataModel.KEY_MMKV_SQUARE_SCENE)){
            //可能存在历史数据，需进行下主动清理
            try{
                JSONObject jsonObject = JSONObject.parseObject(mMMKV.decodeString(SceneDataModel.KEY_MMKV_SQUARE_SCENE));
                long updateTime = (Long) jsonObject.get(SceneDataModel.UPDATE_TIME_KEY);
                String jsonString = JSON.parse(jsonObject.get(SceneDataModel.DATA_KEY).toString()).toString();
                //数据有效期判断
                boolean periodValidity = (System.currentTimeMillis() - updateTime) < UPDATE_INTERVAL;
                if(periodValidity){
                    return JSON.parseObject(jsonString, new TypeReference<List<SceneCategory>>() {});
                }
            }catch (Exception e){
                LogUtil.d(TAG,"old data change ,need clear first");
                mMMKV.remove(KEY_MMKV_SQUARE_SCENE);
                return null;
            }

        }
        return null;
    }
    /**
     * 返回本地保存数据，内置有效期判断
     * @return
     */
    public @Nullable
    List<SceneCategory> getLocalSquareScene(){
        JSONObject jsonObject = JSONObject.parseObject(mMMKV.decodeString(KEY_MMKV_SQUARE_SCENE));
        if (null != jsonObject) {
            String jsonString = JSON.parse(jsonObject.get(DATA_KEY).toString()).toString();
            //仅返回内容数据
            return JSON.parseObject(jsonString, new TypeReference<List<SceneCategory>>() {
            });
        }
        return new ArrayList<>();
    }


    /**
     * 针对云端返回的情景列表进行排序的比较器
     */
    private static final class SceneBeanComparator implements Comparator<SceneBean> {
        @Override
        public int compare(SceneBean s1, SceneBean s2) {
            if(s1.getTimestamp() >= s2.getTimestamp()){
                return -1;
            }else{
                return 1;
            }
        }
    }

    public void notifySceneBeanExecuteFinish(SceneBean sceneBean){
        mExecuteSceneBeanEmitter.onNext(sceneBean);
    }
    public Observable<SceneBean> getExecuteSceneBeanObservable(){
        return Observable.create(mExecuteSceneBeanEmitter::add).observeOn(AndroidSchedulers.mainThread()).subscribeOn(Schedulers.io());
    }
    public Observable<List<SceneCategory>> getLocalUserSceneObservable(){
        return Observable.create(mLocalUserScenesEmitter::add).observeOn(AndroidSchedulers.mainThread()).subscribeOn(Schedulers.io());
    }
}
