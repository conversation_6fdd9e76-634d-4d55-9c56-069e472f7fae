package com.dfl.smartscene.ccs.busevent;

/**
 * <AUTHOR>
 * @date ：2022/12/13
 * @description ：
 */
public class ImageOrVideoFullViewEvent {

    private int isFullState; // 0:close, 1:open

    public ImageOrVideoFullViewEvent(int isFullState) {
        this.isFullState = isFullState;
    }

    public int getIsFullState() {
        return isFullState;
    }

    public void setIsFullState(int isFullState) {
        this.isFullState = isFullState;
    }
}
