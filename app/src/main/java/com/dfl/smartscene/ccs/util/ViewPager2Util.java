package com.dfl.smartscene.ccs.util;

import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.dfl.dflcommonlibs.log.LogUtil;

import java.lang.reflect.Field;

/**
 * @author: huangzezheng
 * @date: 2021/12/15
 */
public class ViewPager2Util {
    //降低滑动灵敏度
    public static void reducedSlidingSensitivity(ViewPager2 viewPager2, int distance){
        try {
            Field recyclerViewField = ViewPager2.class.getDeclaredField("mRecyclerView");
            recyclerViewField.setAccessible(true);
            Object recyclerView = recyclerViewField.get(viewPager2);
            Field disFiled = RecyclerView.class.getDeclaredField("mTouchSlop");
            disFiled.setAccessible(true);
            disFiled.set(recyclerView,distance);
        }catch (Exception e){
            LogUtil.e("ViewPager2Util",e.getMessage());
        }
    }

}
