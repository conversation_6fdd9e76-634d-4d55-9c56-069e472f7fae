package com.dfl.smartscene.ccs.base;

import android.content.res.Configuration;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.viewpager2.widget.ViewPager2;

import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.constant.ConstantViewValue;
import com.dfl.smartscene.ccs.model.manager.SceneControlManager;
import com.dfl.smartscene.ccs.util.ExpandTouchArea;
import com.dfl.smartscene.ccs.util.ViewPager2Util;
import com.dfl.smartscene.ccs.view.ViewControlManager;
import com.dfl.smartscene.ccs.view.adapter.MutiTitleFragmentAdapter;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;

/**
 * 首页的Base Fragment，包含一个标题栏与一个fragmentnavhost
 *
 * @author: huangzezheng
 * @date: 2021/10/19
 */
public abstract class MultiTitleBaseFragment extends Fragment {
    protected TabLayout mTabLayoutTitle;
    private ImageView mImageViewCloseButton;
    protected ViewPager2 mViewPager2Content;
    private String[] mPageNames;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mPageNames = getPageNames();
    }

    public abstract String[] getPageNames();

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return getLayoutInflater().inflate(R.layout.fragment_multi_title, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mTabLayoutTitle = view.findViewById(R.id.tab_layout_hotnew_title);
        mViewPager2Content = view.findViewById(R.id.viewpager2_hotnew_fragment_content);
        mViewPager2Content.setUserInputEnabled(isUserInputEnabled());

        mImageViewCloseButton = view.findViewById(R.id.button_hotnew_close);
        MutiTitleFragmentAdapter mutiTitleFragmentAdapter = new MutiTitleFragmentAdapter(getChildFragmentManager(), getLifecycle(), mPageNames);
        mViewPager2Content.setAdapter(mutiTitleFragmentAdapter);
//        mViewPager2Content.setUserInputEnabled(false);
        ViewPager2Util.reducedSlidingSensitivity(mViewPager2Content, 60);
        new TabLayoutMediator(mTabLayoutTitle, mViewPager2Content, false, false,
                (tab, position) -> {
                    String tabText = "";
                    switch (mPageNames[position]) {
                        case ConstantViewValue.PAGE_MAIN_LIBRARY:
                            tabText = view.getResources().getString(R.string.string_page_library_title);
                            break;
                        case ConstantViewValue.PAGE_MAIN_MY:
                            tabText = view.getResources().getString(R.string.string_page_my_title);
                            break;
                        default:
                            break;
                    }
                    tab.setText(tabText);
                }).attach();
//        //根据传入参数判断展示哪个页面
//        if (getArguments() != null && getArguments().containsKey(ConstantValue.KEY_CONTENT_PAGE)) {
//            int position = Arrays.binarySearch(mPageNames, getArguments().getString(ConstantValue.KEY_CONTENT_PAGE));
//            mTabLayoutTitle.selectTab(mTabLayoutTitle.getTabAt(position));
//        }

        //关闭按钮点击事件
        mImageViewCloseButton.setOnClickListener(v -> {
            SceneControlManager.getInstance().exitScene();
            ViewControlManager.closeApp();
        });
        ExpandTouchArea.expandTouchArea(mImageViewCloseButton, 20);

//        view.findViewById(R.id.btn_icon_name).setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//            }
//        });
//        view.findViewById(R.id.btn_icon).setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//
//            }
//        });

    }

    protected void changePage(int pos) {
        mViewPager2Content.setCurrentItem(pos, false);
    }

    protected boolean isUserInputEnabled() {
        return false;
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        getChildFragmentManager().getFragments().forEach(fragment -> fragment.onHiddenChanged(hidden));
    }

    @Override
    public void onDestroyView() {
        mTabLayoutTitle = null;
        mImageViewCloseButton = null;
        mViewPager2Content.setAdapter(null);
        mViewPager2Content.removeAllViews();
        mViewPager2Content = null;
        super.onDestroyView();
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (mTabLayoutTitle != null) {
            mTabLayoutTitle.setTabTextColors(
                    ContextCompat.getColor(requireContext(), R.color.color_text_secondary_label)
                    , ContextCompat.getColor(requireContext(), R.color.color_text_first_label));
            mTabLayoutTitle.setSelectedTabIndicatorColor(getResources().getColor(R.color.color_text_selected));
        }
    }
}
