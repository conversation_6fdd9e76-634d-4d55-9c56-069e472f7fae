package com.dfl.smartscene.ccs.service;

import static com.dfl.smartscene.ccs.model.manager.SceneController.STATE_DESTROY;
import static com.dfl.smartscene.ccs.model.manager.SceneController.STATE_IDLE;
import static com.dfl.smartscene.ccs.model.manager.SceneController.STATE_PAUSE;
import static com.dfl.smartscene.ccs.model.manager.SceneController.STATE_START;
import static com.dfl.smartscene.ccs.model.manager.SceneController.STATE_STOP;

import android.content.Intent;
import android.os.RemoteException;

import com.alibaba.fastjson.JSON;
import com.dfl.smartscene.INewScenePatternService;
import com.dfl.smartscene.INewScenePatternServiceCallback;
import com.dfl.smartscene.INewSceneRequestServiceCallback;
import com.dfl.smartscene.ccs.MainActivity;
import com.dfl.smartscene.ccs.bean.SceneBean;
import com.dfl.smartscene.ccs.bean.SceneCategory;
import com.dfl.smartscene.ccs.constant.ConstantViewValue;
import com.dfl.smartscene.ccs.core.SceneStateListener;
import com.dfl.smartscene.ccs.library.app.ScenePatternApp;
import com.dfl.smartscene.ccs.model.DriveModel;
import com.dfl.smartscene.ccs.model.SceneDataModel;
import com.dfl.smartscene.ccs.model.SurpriseEggListModel;
import com.dfl.smartscene.ccs.model.manager.CarConfigManager;
import com.dfl.smartscene.ccs.model.manager.SceneControlManager;
import com.dfl.smartscene.ccs.model.manager.SceneController;
import com.dfl.smartscene.ccs.util.HandlerUtil;
import com.iauto.uibase.utils.MLog;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

public class ScenePatternServiceStub extends INewScenePatternService.Stub implements SceneStateListener {
    private static final String TAG = "ScenePatternServiceStub";
    private ArrayList <INewScenePatternServiceCallback> mCallbacks = new ArrayList<>();

    private ScenePatternServiceStub() {
        SceneControlManager.getInstance().registerSceneStateListener(this);
    }

    private static class Holder{
        private static final ScenePatternServiceStub INSTANCE = new ScenePatternServiceStub();
    }

    public static ScenePatternServiceStub getInstance(){
        return Holder.INSTANCE;
    }

    @Override
    public void registerScenePatternCallback(INewScenePatternServiceCallback callback) throws RemoteException {
        MLog.d(TAG,"->registerEventTrace()");
        mCallbacks.add(callback);
    }

    @Override
    public void unregisterScenePatternCallback(INewScenePatternServiceCallback callback) throws RemoteException {
        MLog.d(TAG,"->unregisterEventTrace()");
        mCallbacks.remove(callback);
    }

    @Override
    public void reqScenePattern(INewSceneRequestServiceCallback callback) throws RemoteException {
        SceneDataModel.getInstance().requestCloudSceneList().subscribe(new Observer<List<SceneCategory>>() {
            @Override
            public void onSubscribe(Disposable d) {

            }

            @Override
            public void onNext(List<SceneCategory> sceneCategories) {
                try {
                    callback.onSceneListCallback(JSON.toJSONString(sceneCategories));
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
                try {
                    callback.onCompletedCallback();
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onComplete() {

            }
        });
    }

    @Override
    public boolean setScenePatternRunning(String id,int action) {
            String sceneId = id;
            switch (action){
                case STATE_START:
                case STATE_PAUSE:
                    HandlerUtil.getMainHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            SceneControlManager.getInstance().exitScene();
                        }
                    });
                    break;
                case STATE_IDLE:
                case STATE_STOP:
                case STATE_DESTROY:
                    MLog.d(TAG, "da kai ye mian!!");
                    HandlerUtil.getMainHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            // 跳转到舒压模式页面
                            Intent intent = new Intent(ScenePatternApp.getInstance(), MainActivity.class);
                            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            intent.putExtra("ViewType","Scene");
                            intent.putExtra("INTENT_KEY_SCENE_ID",sceneId);
                            ScenePatternApp.getInstance().startActivity(intent);
                        }
                    });
                    break;
                default:
                    break;

            }
            return true;
        }

    @Override
    public int getSceneStatus() throws RemoteException {
        SceneController sceneController = SceneControlManager.getInstance().getSceneController();
        if (null != sceneController){
            int sceneStatus = sceneController.getsState();
            MLog.d(TAG,"->getSceneStatus(): sceneStatus"+sceneStatus);
            return sceneStatus;
        }
        return STATE_IDLE;
    }

    @Override
    public boolean getSceneEnabled(String id) throws RemoteException {
        SceneBean sceneBean = SceneDataModel.getInstance().findLibrarySceneBeanById(id);
        if(CarConfigManager.getInstance().checkOperationPLimit(sceneBean.getActionOperations()) && !DriveModel.getInstance().isPGear()){
            return false;
        }else {
            return true;
        }
    }

    @Override
    public boolean reqSurpriseEgg(int keyCode) throws RemoteException {
        boolean result = SurpriseEggListModel.getInstance().requestSurpriseEggDisplayData(keyCode);
        if (!result) {
            MLog.d(TAG, "->displaySurpriseEgg() : failed");
        }
        return result;
    }

    @Override
    public void onScenePrepare() {
        for (INewScenePatternServiceCallback callback:mCallbacks) {
            try {
                callback.onSceneStatusChange(SceneControlManager.getInstance().getSceneController().getSceneId(), ConstantViewValue.SceneBeanStatus.STATE_START);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void onSceneStart() {
        for (INewScenePatternServiceCallback callback:mCallbacks) {
            try {
                callback.onSceneStatusChange(SceneControlManager.getInstance().getSceneController().getSceneId(), ConstantViewValue.SceneBeanStatus.STATE_START);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void onSceneStop() {
        for (INewScenePatternServiceCallback callback:mCallbacks) {
            try {
                callback.onSceneStatusChange(SceneControlManager.getInstance().getSceneController().getSceneId(), ConstantViewValue.SceneBeanStatus.STATE_STOP);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void onScenePause() {
        for (INewScenePatternServiceCallback callback:mCallbacks) {
            try {
                if (SceneControlManager.getInstance().getSceneController() != null) {
                    callback.onSceneStatusChange(SceneControlManager.getInstance().getSceneController().getSceneId(), ConstantViewValue.SceneBeanStatus.STATE_PAUSE);
                }
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void onSceneResume() {
        for (INewScenePatternServiceCallback callback:mCallbacks) {
            try {
                callback.onSceneStatusChange(SceneControlManager.getInstance().getSceneController().getSceneId(), ConstantViewValue.SceneBeanStatus.STATE_START);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void onSceneDestroy() {
        for (INewScenePatternServiceCallback callback:mCallbacks) {
            try {
                callback.onSceneStatusChange(SceneControlManager.getInstance().getSceneController().getSceneId(), ConstantViewValue.SceneBeanStatus.STATE_DESTROY);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }


}
