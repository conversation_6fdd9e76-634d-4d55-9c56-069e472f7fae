package com.dfl.smartscene.ccs.util;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.PixelFormat;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.ArrayMap;
import android.view.Gravity;
import android.view.WindowManager;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.dfl.api.base.APICreateCallback;
import com.dfl.api.base.APIStateType;
import com.dfl.api.base.BaseManager;
import com.dfl.api.base.IBaseAPI;
import com.dfl.api.base.NullServiceException;
import com.dfl.api.da.systemview.ISystemView;
import com.dfl.api.da.systemview.ISystemViewCallback;
import com.dfl.api.da.systemview.SystemViewIdDef;
import com.dfl.smartscene.R;
import com.dfl.smartscene.ccs.constant.Constants;
import com.dfl.smartscene.ccs.constant.ScenePatternFuncDef;
import com.dfl.smartscene.ccs.model.RelieveStressPatternModel;
import com.dfl.smartscene.ccs.view.popup.RelieveStressPatternFullView;
import com.dfl.smartscene.ccs.view.popup.SurpriseEggPlayView;
import com.dfl.smartscene.util.SysViewDef;
import com.iauto.uibase.utils.MLog;
import com.iauto.uibase.view.MViewBase;
import com.iauto.uibase.view.MWindowManager;

import java.util.ArrayList;
import java.util.List;

public class SysViewControl {
    private static final String TAG = "SysViewControl";
    @SuppressLint("StaticFieldLeak")
    private static SysViewControl sSysViewControl = null;
    private Context mContext = null;
    private final ArrayMap<String, MViewBase> mSysViews = new ArrayMap<>();
    private Handler mHandler = null;
    private ISystemView mISystemView = null;
    private static final int MSG_COMPETE_FINISHED = 0;
    private static final int MSG_SYS_VIEW_CHANGED = 1;
    private int mScenePatternType = ScenePatternFuncDef.SYS_VIEW_CTRL_PATTERN_NONE;


    private SysViewControl() {
        mHandler = new Handler(Looper.getMainLooper()){
            @Override
            public void handleMessage(@NonNull Message msg) {
                super.handleMessage(msg);
                int what = msg.what;
                MLog.d(TAG, "handleMessage" + what);
                switch (what){
                    case MSG_COMPETE_FINISHED: {
                        dealCompeteFinished(msg.arg1);
                        break;
                        }
                    case MSG_SYS_VIEW_CHANGED: {
                        Bundle bundle = msg.getData();
                        if (null != bundle) {
                            ArrayList<Integer> list = bundle.getIntegerArrayList("CurrentViews");
                            dealSysViewChanged(list);
                        }
                        break;
                        }
                    default:
                        break;
                }
            }
        };
    }

    public static SysViewControl getInstance() {
        if (null == sSysViewControl) {
            sSysViewControl = new SysViewControl();
        }
        return sSysViewControl;
    }

    public void init(Context context) {
        MLog.d(TAG, "init");
        mContext = context;
        if (null == mISystemView) {
            BaseManager.create(mContext, Constants.DFL_DA_SYSTEMVIEW_SYSTEMVIEW_SERVICE, new APICreateCallback() {
                @Override
                public void onAPICreateCallback(APIStateType.APIState apiStateType, String s, IBaseAPI iBaseAPI) {
                    // 以下判断不作为正式模板，具体参考CustomAPI使用示例
                    if (null != iBaseAPI && APIStateType.APIState.SUCCESS == apiStateType && s.equals(Constants.DFL_DA_SYSTEMVIEW_SYSTEMVIEW_SERVICE)) {
                        MLog.d(TAG, "onAPICreateCallback");
                        mISystemView = (ISystemView) iBaseAPI;
                    }
                }

                @Override
                public void onMethodStateCallback(APIStateType.APIState apiState, String s) {
                    MLog.d(TAG, "onMethodStateCallback  "+apiState+" "+s);
                }
            });
        }
    }

    /**
     * 注册窗口竞合结果的监听（要求申请显示时注册，结束显示时反注册）
     * */
    private void registerSystemViewCallback(){
        if(null == mISystemView){
            return;
        }
        List<Integer> list  = new ArrayList<>();
        list.add(SystemViewIdDef.SYSTEMVIEW_ID_SCENEMODE_0);
        list.add(SystemViewIdDef.SYSTEMVIEW_ID_SCENEMODE_1);
        MLog.d(TAG, "register system view callback");
        // 只有状态Id是List中的元素时，才能接收mISystemViewCallback的onCompeteFinished回调，不影响onSysViewChanged的接收
        try {
            mISystemView.registerSysViewCallback(mISystemViewCallback, list);
        } catch (Exception e) {
            MLog.e(TAG, "onAPICreateCallback ex:"+e.getMessage());
        }
    }

    /**
     * 反注册窗口竞合结果的监听（移除视图时反注册）
     */
    public void unregisterSystemViewCallback(){
        if(null == mISystemView){
            return;
        }
        MLog.d(TAG, "unregister system view callback");
        try {
            mISystemView.unregisterSysViewCallback(mISystemViewCallback);
        } catch (Exception e) {
            MLog.e(TAG, "onAPICreateCallback ex:"+e.getMessage());
        }
    }

    private final ISystemViewCallback mISystemViewCallback = new ISystemViewCallback() {

        /**
         * 竞合结果通知
         * @param i 系统弹窗画面需要显示的状态Id
         */
        @Override
        public void onCompeteFinished(int i) {
            MLog.d(TAG, "onCompeteFinished: [ " + i+" ]");
            if (Looper.myLooper() == Looper.getMainLooper()) {
                dealCompeteFinished(i);
            }
            else {
                Message msg = mHandler.obtainMessage();
                msg.what = MSG_COMPETE_FINISHED;
                msg.arg1 = i;
                mHandler.sendMessage(msg);
            }
        }

        /**
         * 当前显示的系统弹窗画面变更
         * @param list 当前显示的系统弹窗画面
         */
        @Override
        public void onSysViewChanged(List list) {
            MLog.d(TAG, "onSysViewChanged " + list);
            ArrayList<Integer> arrayList = new ArrayList<>();
            arrayList.addAll(list);
            if (Looper.myLooper() == Looper.getMainLooper()) {
                dealSysViewChanged(arrayList);
            }
            else {
                Message msg = mHandler.obtainMessage();
                msg.what = MSG_SYS_VIEW_CHANGED;
                Bundle bundle = new Bundle();
                bundle.putIntegerArrayList("CurrentViews", arrayList);
                msg.setData(bundle);
                mHandler.sendMessage(msg);
            }
        }
    };


    /**
     * 按照竞合结果执行对应的画面操作(显示/隐藏/变更系统弹窗)
     * @param id 画面状态Id
     */
    private void dealCompeteFinished(int id) {
        MLog.d(TAG, "dealCompeteFinished: " + id);
        switch (id) {
            case SystemViewIdDef.SYSTEMVIEW_ID_SCENEMODE_1: {
                if (ScenePatternFuncDef.SYS_VIEW_CTRL_PATTERN_RELIEVE == mScenePatternType) {
                    openRelieveStressPatternFullView();
                } else if (ScenePatternFuncDef.SYS_VIEW_CTRL_PATTERN_SURPRISE == mScenePatternType) {
                    openSurpriseEggPlayFullView();
                } else {
                    // do nothing
                }
                break;
            }
            case SystemViewIdDef.SYSTEMVIEW_ID_SCENEMODE_0: {
//                closeRelieveStressPatternFullView();
                break;
            }


            default:{}
        }
    }


    /**
     * 当前显示系统弹窗变更后处理
     * @param list 当前显示的系统弹窗
     */
    private void dealSysViewChanged(ArrayList<Integer> list) {
        MLog.d(TAG, "dealSysViewChanged: " + list);
    }

    public void reqOpenRelieveStressPatternFullView(int type) {
        MLog.d(TAG, "reqOpenRelieveStressPatternFullView: ");
        mScenePatternType = type;
//        openSurpriseEggPlayFullView();
        if (null == mISystemView) {
            MLog.e(TAG, "reqOpenRelieveStressPatternFullView: Error");
            return;
        }
        registerSystemViewCallback();
        try {
            mISystemView.requestOpenSysView(SystemViewIdDef.SYSTEMVIEW_ID_SCENEMODE_1);
        } catch (NullServiceException e) {
            MLog.e(TAG, "requestOpenSysView catch NullServiceException");
        }
    }

    public void reqCloseRelieveStressPatternFullView() {
        MLog.d(TAG, "reqcloseRelieveStressPatternFullView");
        closeRelieveStressPatternFullView();
        if (null == mISystemView) {
            MLog.e(TAG, "reqcloseRelieveStressPatternFullView: Error");
            return;
        }
        unregisterSystemViewCallback();
        try {
            mISystemView.requestSysViewClose(SystemViewIdDef.SYSTEMVIEW_ID_SCENEMODE_0);
        } catch (NullServiceException e) {
            MLog.e(TAG, "requestSysViewClose catch NullServiceException");
        }
    }

    public void reqOpenSurpriseEggPlayFullView(int type) {
        MLog.d(TAG, "reqOpenSurpriseEggPlayFullView: ");
        mScenePatternType = type;
        if (null == mISystemView) {
            MLog.e(TAG, "reqOpenSurpriseEggPlayFullView: Error");
            return;
        }
        registerSystemViewCallback();
        try {
            mISystemView.requestOpenSysView(SystemViewIdDef.SYSTEMVIEW_ID_SCENEMODE_1);
        } catch (NullServiceException e) {
            MLog.e(TAG, "requestOpenSysView catch NullServiceException");
        }
    }

    public void reqCloseSurpriseEggPlayFullView() {
        MLog.d(TAG, "reqCloseSurpriseEggPlayFullView");
        ToastManager.getInstance().showToast(mContext.getString(R.string.string_surprise_egg_play_end),
                Toast.LENGTH_SHORT,
                false);
        closeSurpriseEggPlayFullView();
        if (null == mISystemView) {
            MLog.e(TAG, "reqCloseSurpriseEggPlayFullView: Error");
            return;
        }
        unregisterSystemViewCallback();
        try {
            mISystemView.requestSysViewClose(SystemViewIdDef.SYSTEMVIEW_ID_SCENEMODE_0);
        } catch (NullServiceException e) {
            MLog.e(TAG, "requestSysViewClose catch NullServiceException");
        }
    }

    public void openRelieveStressPatternFullView(){
        MLog.d(TAG, "openRelieveStressPatternFullView");
        MViewBase viewBase = getSysView(SysViewDef.SystemId_Relieve_Stress_Pattern_Full_View);

        if (null != viewBase) {

            WindowManager.LayoutParams params = new WindowManager.LayoutParams(
                    WindowManager.LayoutParams.MATCH_PARENT,
                    WindowManager.LayoutParams.MATCH_PARENT,
                    0, 0,
                    SysViewDef.SCENE_PATTERN_LAYER,
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL,
                    PixelFormat.TRANSLUCENT);
            params.gravity = Gravity.TOP | Gravity.START;
            MWindowManager.getInstance().addView(mContext,viewBase, params);

            RelieveStressPatternModel.getInstance().setFullDSPStatus(true);
        }
    }

    /**
     * 执行舒压全屏弹窗的视图移除工作
     */
    public void closeRelieveStressPatternFullView() {
        MLog.d(TAG, "closeRelieveStressPatternFullView");
        String viewId = SysViewDef.SystemId_Relieve_Stress_Pattern_Full_View;
        MViewBase view = mSysViews.get(viewId);
        if (null != view) {
            MWindowManager.getInstance().removeView(mContext,view);
            RelieveStressPatternModel.getInstance().setFullDSPStatus(false);
            mSysViews.remove(viewId);
            MLog.d(TAG, "remove view");
        }
    }

    public void openSurpriseEggPlayFullView(){
        MLog.d(TAG, "openSurpriseEggPlayFullView");
        MViewBase viewBase = getSysView(SysViewDef.SystemId_Surprise_egg_Play_View);
        if (null != viewBase) {

            WindowManager.LayoutParams params = new WindowManager.LayoutParams(
                    WindowManager.LayoutParams.MATCH_PARENT,
                    WindowManager.LayoutParams.MATCH_PARENT,
                    0, 0,
                    SysViewDef.SCENE_PATTERN_LAYER,
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL,
                    PixelFormat.TRANSLUCENT);
            params.gravity = Gravity.TOP | Gravity.START;
            MWindowManager.getInstance().addView(mContext,viewBase, params);
        }
    }

    /**
     * 执行惊喜彩蛋弹窗的视图移除工作
     */
    public void closeSurpriseEggPlayFullView() {
        MLog.d(TAG, "closeSurpriseEggPlayFullView, origin map size = "+mSysViews.size());
        String viewId = SysViewDef.SystemId_Surprise_egg_Play_View;
        MViewBase view = mSysViews.get(viewId);;
        if (null != view) {
            MWindowManager.getInstance().removeView(mContext, view);
            mSysViews.remove(viewId);
            MLog.d(TAG, "remove view, new map size = "+mSysViews.size());
        }
    }

    private MViewBase getSysView(String viewId) {
        MViewBase view = mSysViews.get(viewId);
        if (null == view) {
            view = createSysView(viewId);
            if (null != view) {
                mSysViews.put(viewId, view);
            }
        }
        return view;
    }

    private MViewBase createSysView(String viewId) {
        MViewBase viewBase = null;
        switch (viewId) {
            case SysViewDef.SystemId_Relieve_Stress_Pattern_Full_View:{
                viewBase = new RelieveStressPatternFullView(mContext);
                break;
            }

            case SysViewDef.SystemId_Surprise_egg_Play_View:{
                viewBase = new SurpriseEggPlayView(mContext);
                break;
            }

            default:{}
        }
        return viewBase;
    }


}
