plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace 'com.dfl.soacenter'
    compileSdk 33

    defaultConfig {
        minSdk 30
        versionName "V" + rootProject.versionName
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = '11'
    }
    buildFeatures {
        aidl true
    }

    configurations {
        all {
            exclude group: 'com.google.guava', module: 'listenablefuture'
        }
    }
}

dependencies {
    api fileTree(dir: 'libs', include: ['*.aar', '*.jar'], exclude: [])
    api project(path: ':common')
    releaseCompileOnly files("../common/libs/customapi_${customapi_version}.jar")
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}